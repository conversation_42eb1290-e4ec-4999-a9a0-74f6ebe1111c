using Entitys;
using YseStore.Model.Entities.Blog;
using YseStore.Model.Response.Products;

namespace YseStore.Model.Response.Visual
{
    /// <summary>
    /// 可视化页面响应模型
    /// </summary>
    public class VisualPageResponse
    {
        /// <summary>
        /// 插件列表
        /// </summary>
        public List<visual_plugins> Plugins { get; set; }
        /// <summary>
        /// 博客列表
        /// </summary>
        public List<BlogNew> BlogList { get; set; }

        /// <summary>
        /// 按类型分组的插件
        /// </summary>
        public Dictionary<string, List<visual_plugins>> PluginsByType { get; set; }

        public VisualPageResponse()
        {
            Plugins = new List<visual_plugins>();
            BlogList = new List<BlogNew>();
            PluginsByType = new Dictionary<string, List<visual_plugins>>();
        }
    }

    /// <summary>
    /// 可视化页面请求参数
    /// </summary>
    public class VisualPageRequest
    {
        /// <summary>
        /// 页面类型（如：index、products等）
        /// </summary>
        public string Pages { get; set; } = "index";
    }
}