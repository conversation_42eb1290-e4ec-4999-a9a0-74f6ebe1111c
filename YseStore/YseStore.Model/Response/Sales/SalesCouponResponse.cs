using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Model.Enums;

namespace YseStore.Model.Response.Sales
{
    public class SalesCouponResponse
    {
        public System.Int32 CId { get; set; }

        /// <summary>
        /// 优惠码
        /// </summary>
        public System.String CouponNumber { get; set; }

        /// <summary>
        /// 优惠折扣（百分比）
        /// </summary>
        public System.Int32? Discount { get; set; }

        /// <summary>
        /// 优惠减价金额
        /// </summary>
        public System.Decimal? Money { get; set; }

        /// <summary>
        /// 优惠券类型 (0: 折扣券 1:现金券)
        /// </summary>
        public CouponTypeEnum? CouponType { get; set; }

        /// <summary>
        /// 使用要求 (0:最低消费金额 1:最低购买数量)
        /// </summary>
        public ConditionTypeEnum ConditionType { get; set; }

        /// <summary>
        /// 优惠券类型的值
        /// </summary>
        public System.Decimal CouponTypeValue { get; set; }
        public string Rules {
            get
            {
                var ruleBuilder = new StringBuilder();

                // 使用要求条件
                ruleBuilder.Append(ConditionType == ConditionTypeEnum.MinAmount ?
                    $"满${ConditionPrice}，" :
                    $"满{ConditionQty}件可用，");

                // 优惠类型逻辑
                if (CouponType == CouponTypeEnum.Discount) // 折扣券
                {
                    ruleBuilder.Append($"{100 - CouponTypeValue} % off");
                    if (IsMaxAmount && MaxAmount > 0) // 如果有最高抵扣金额
                    {
                        ruleBuilder.Append($"，最高抵扣{MaxAmount}元");
                    }
                }
                else // 现金券
                {
                    ruleBuilder.Append($"减${CouponTypeValue}");
                }

                //// 有效期展示逻辑
                //ruleBuilder.Append(ValidityType == ValidityTypeEnum.Fixed.ToString().ToLower() ?
                //    $"，有效期至{EndTime:yyyy-MM-dd HH:mm}" :
                //    $"，领取后{Duration}{TimeType}内有效");

                return ruleBuilder.ToString();
            }
        }
        public ValueTuple<ActiveStatusEnum,string>  ActiveStatus
        {
            get
            {if (StartTime == null || EndTime == null) return (ActiveStatusEnum.未开始,"");
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
                // 主逻辑判断
                if (ValidityType == ValidityTypeEnum.Receive.ToString().ToLower() ||
                    (StartTime < currentTime && currentTime < EndTime))
                {
                    
                    return (ActiveStatusEnum.进行中,"ing");
                }
                else if (StartTime > currentTime)
                {
                    return (ActiveStatusEnum.未开始,"");
                }
                else
                {
                    return (ActiveStatusEnum.已结束, "end");
                }
            }
        }
        

        /// <summary>
        /// 是否限制最高抵扣金额
        /// </summary>
        public System.Boolean IsMaxAmount { get; set; }

        /// <summary>
        /// 最高抵扣金额
        /// </summary>
        public System.Decimal MaxAmount { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public System.Int32? StartTime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public System.Int32? EndTime { get; set; }
        /// <summary>
        /// 最低消费金额
        /// </summary>
        public System.Decimal ConditionPrice { get; set; }

        /// <summary>
        /// 最低购买数量
        /// </summary>
        public System.Int32 ConditionQty { get; set; }
        /// <summary>
        /// 有效期 (fixed:固定时间 receive:领取时间)
        /// </summary>
        public System.String ValidityType { get; set; }

        /// <summary>
        /// 有效时长
        /// </summary>
        public System.Int32 Duration { get; set; }

        /// <summary>
        /// 时长类型 (day:天 hour:小时)
        /// </summary>
        public System.String TimeType { get; set; }

        /// <summary>
        /// 投放方式 (manual:手动 auto:自动)
        /// </summary>
        public System.String DeliveryMethod { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        public System.Int32? UseNum { get; set; }
        /// <summary>
        /// 不限领取数量
        /// </summary>
        public System.Boolean UnLmQty { get; set; }

        /// <summary>
        /// 领取数量(发放量)
        /// </summary>
        public System.Int32 Qty { get; set; }

        /// <summary>
        /// 已领取数量
        /// </summary>
        public System.Int32 GetNum { get; set; }

        /// <summary>
        /// 已使用次数
        /// </summary>
        public System.Int32 BeUseTimes { get; set; }

       
    }

    public class CouponCount {
        public System.Int32 CId { get; set; }
        public System.Int32 Counts { get; set; }
    }
}
