using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class custom_attributes
    {
        /// <summary>
        /// 
        /// </summary>
        public custom_attributes()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.Int32 TemplateId { get; set; }

        /// <summary>
        /// 模板名称
        /// </summary>
        public System.String Name { get; set; }

        /// <summary>
        /// 模板应用范围
        /// </summary>
        public System.String Apply { get; set; }

        /// <summary>
        /// 模板应用ID
        /// </summary>
        public System.String ID { get; set; }

        /// <summary>
        /// 自定义排序
        /// </summary>
        public System.Int32? MyOrder { get; set; }
    }
}