using SqlSugar;

namespace Entitys
{
    /// <summary>
    /// 
    /// </summary>
    public class user_forgot
    {
        /// <summary>
        /// 
        /// </summary>
        public user_forgot()
        {
        }

        /// <summary>
        /// 
        /// </summary>
        [SugarColumn(IsPrimaryKey = true, IsIdentity = true)]
        public System.UInt32 FId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? UserId { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String EmailEncode { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.String Expiry { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Int32? ResetTime { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public System.Boolean? IsReset { get; set; }
    }
}