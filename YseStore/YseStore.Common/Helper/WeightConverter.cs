using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace YseStore.Common.Helper
{
    /// <summary>
    /// 重量转换器   
    /// </summary>
    public class WeightConverter
    {

        public enum WeightUnit
        {
            kg,//千克
            g,//克
            lb,//磅
            oz//盎司
        }

        /// <summary>
        /// 重量转换器
        /// </summary>
        /// <param name="value"></param>
        /// <param name="fromUnit"></param>
        /// <param name="toUnit"></param>
        /// <returns></returns>
        public static decimal ConvertWeight(decimal value, WeightUnit fromUnit, WeightUnit toUnit = WeightUnit.kg)
        {

            // 首先将所有单位转换为千克
            decimal valueInKilograms = fromUnit switch
            {
                WeightUnit.kg => value,
                WeightUnit.g => value / 1000,
                WeightUnit.lb => value * 0.45359237m,
                WeightUnit.oz => value * 0.028349523125m,
                _ => 0 // 默认值，如果fromUnit无效则返回0
            };

            // 然后从千克转换为目标单位
            return toUnit switch
            {
                WeightUnit.kg => valueInKilograms,
                WeightUnit.g => valueInKilograms * 1000,
                WeightUnit.lb => valueInKilograms / 0.45359237m,
                WeightUnit.oz => valueInKilograms / 0.028349523125m,
                _ => 0
            };
        }




    }
}
