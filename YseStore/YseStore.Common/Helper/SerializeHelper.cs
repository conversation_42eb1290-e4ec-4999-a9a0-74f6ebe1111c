using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using YseStore.Common.Helper;

namespace YseStore.Common
{


    public static class JsonUtil
    {

      
        private static JsonSerializerSettings options = new JsonSerializerSettings
        {
            DateFormatString = "yyyy-MM-dd HH:mm:ss"
        };

        public static string ToJson(this object obj, bool allowCircularRef = false)
        {
            string text = "{}";
            if (allowCircularRef)
            {
                JsonSerializerSettings settings = new JsonSerializerSettings
                {
                    ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                    DateFormatString = "yyyy-MM-dd HH:mm:ss"
                };
                return JsonConvert.SerializeObject(obj, settings);
            }

            return JsonConvert.SerializeObject(obj, options);
        }

        /// <summary>
        /// 专门用于处理包含JSON字符串字段的对象序列化，避免双重转义
        /// </summary>
        /// <param name="obj">要序列化的对象</param>
        /// <returns>格式化的JSON字符串，内部JSON字符串不会被转义</returns>
        public static string ToJsonWithParsedFields(this object obj)
        {
            try
            {
                // 先序列化为JToken
                var jToken = JToken.FromObject(obj);

                // 递归处理所有可能的JSON字符串字段
                ProcessJsonStringFields(jToken);

                // 格式化输出
                return jToken.ToString(Formatting.Indented);
            }
            catch (Exception)
            {
                // 如果处理失败，回退到普通序列化
                return JsonConvert.SerializeObject(obj, Formatting.Indented);
            }
        }

        /// <summary>
        /// 递归处理JToken中的JSON字符串字段
        /// </summary>
        /// <param name="token">要处理的JToken</param>
        private static void ProcessJsonStringFields(JToken token)
        {
            if (token is JObject jObject)
            {
                var propertiesToUpdate = new List<(string key, JToken value)>();

                foreach (var property in jObject.Properties().ToList())
                {
                    if (property.Value.Type == JTokenType.String)
                    {
                        var stringValue = property.Value.ToString();

                        // 检查是否是JSON字符串（以{或[开头）
                        if ((stringValue.StartsWith("{") && stringValue.EndsWith("}")) ||
                            (stringValue.StartsWith("[") && stringValue.EndsWith("]")))
                        {
                            try
                            {
                                // 尝试解析为JSON对象
                                var parsedJson = JToken.Parse(stringValue);
                                propertiesToUpdate.Add((property.Name, parsedJson));
                            }
                            catch
                            {
                                // 如果解析失败，保持原样
                            }
                        }
                    }
                    else
                    {
                        // 递归处理嵌套对象
                        ProcessJsonStringFields(property.Value);
                    }
                }

                // 更新解析后的JSON字段
                foreach (var (key, value) in propertiesToUpdate)
                {
                    jObject[key] = value;
                }
            }
            else if (token is JArray jArray)
            {
                foreach (var item in jArray)
                {
                    ProcessJsonStringFields(item);
                }
            }
        }

        public static T JsonToObj<T>(this string json, bool allowCircularRef = false)
        {
            try
            {

                if (json.IsNullOrEmpty())
                {
                    return default(T);
                }


                T val = default(T);
                if (allowCircularRef)
                {
                    JsonSerializerSettings settings = new JsonSerializerSettings
                    {
                        ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                        DateFormatString = "yyyy-MM-dd HH:mm:ss"
                    };
                    return JsonConvert.DeserializeObject<T>(json, settings);
                }

                return JsonConvert.DeserializeObject<T>(json, options);
            }
            catch (Exception ex)
            {
                // 处理异常，可能是JSON格式错误或其他问题
                Console.WriteLine($"JSON反序列化失败: {ex.Message}");
                return default(T);

            }
       
        }
    }

}
