using Aop.Api.Domain;
using Entitys;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using OfficeOpenXml.FormulaParsing.Excel.Functions.Math;
using SnowflakeId.AutoRegister.Core;
using System.Text;
using System.Web;
using Wangkanai.Extensions;
using YseStore.Common;
using YseStore.Common.Cache;
using YseStore.Common.Const;
using YseStore.Common.Helper;
using YseStore.Ext;
using YseStore.IService;
using YseStore.IService.Products;
using YseStore.IService.Shopping;
using YseStore.Model.FromBody;
using YseStore.Model.VM;
using YseStore.Model.VM.Cart;
using YseStore.Model.VM.Payment.Payoneer;
using YseStore.Model.VM.Sales;

namespace YseStore.Controllers
{
    /// <summary>
    /// 购物车控制器
    /// </summary>
    public class CartController : BaseController
    {
        private readonly IShoppingCartServices _cartServices;
        private readonly IShoppingCartLogServices _shoppingCartLogServices;
        private readonly IProductService _productService;
        private readonly IStringLocalizer<CartController> _localizer;
        private readonly ICaching _caching;
        private readonly ICurrencyService _currencyService;

        public CartController(IShoppingCartServices shoppingCartServices, IShoppingCartLogServices shoppingCartLogServices, IProductService productService, IStringLocalizer<CartController> localizer, ICaching caching, ICurrencyService currencyService)
        {
            // 可以在这里注入服务
            _cartServices = shoppingCartServices;
            _shoppingCartLogServices = shoppingCartLogServices;
            _productService = productService;
            _localizer = localizer;
            _caching = caching;
            _currencyService = currencyService;
        }

        /*
         * 添加单个货品到购物车
         * 添加多个货品到购物车
         * 删除
         * 清空购物车
         * 设置购物车商品数量
         * 更新购物车
         * 验证购物车商品CheckoutCart
         */

        /*
         * 不可选产品
         * 1.购物车库存id不是有效的id时，就展示为不可选产品
         * 2.产品规格改变，也设置为不可选产品
         * 产品表  IsCombination 规格
         */

        #region 添加单个货品到购物车

        /// <summary>
        /// 添加单个货品到购物车
        /// 返回购物车产品的详情
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> AddCart([FromBody] FMCartAdd entity)
        {
            var jm = new WebApiCallBack();
            List<string> variantsId = new List<string>();
            if (!entity.variantsId.IsNullOrEmpty())
            {
                variantsId = entity.variantsId.Split(',').ToList();
            }
            else
            {
                variantsId.Add("");
            }

            if (CurrentUserId > 0)//在线客户
            {
                jm = await _cartServices.Add(CurrentUserId, entity.ProductId, variantsId, entity.Nums, 1, entity.cartType, CurrentLang, OvId: entity.ovId);

            }
            else//访客购买
            {
                jm = await _cartServices.Add(CurrentUserSessionId, entity.ProductId, variantsId, entity.Nums, 1, entity.cartType, CurrentLang, OvId: entity.ovId);

            }


            #region 添加赠品
            //获取用户币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();
            var cartData = new WebApiCallBack<CartDto>();
            //判断是否有赠品
            if (CurrentUserId > 0)//在线客户
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency);
            }
            else
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency);
            }

            if (cartData.status == true)
            {
                //添加赠品
                if (CurrentUserId > 0)
                {
                    await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserId, CurrentLang);
                }
                else
                {
                    await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserSessionId, CurrentLang);
                }
            }

            #endregion

            if (jm.status == true)
            {
                if (CurrentUserId > 0)//在线客户
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserId);
                    jm.otherData = count;

                    //更新jwt Token
                    await SetTokenCartCount(count);
                }
                else
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserSessionId);
                    jm.otherData = count;

                    //设置session
                    CurrentCartCount = count;
                }
            }

            jm.msg = _localizer[jm.msg];

            return Json(jm);
        }

        #endregion 添加单个货品到购物车


        #region 立即购买
        /// <summary>
        /// 立即购买
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> BuyItNow([FromBody] FMCartAdd entity)
        {
            var jm = new WebApiCallBack();
            List<string> variantsId = new List<string>();
            if (!entity.variantsId.IsNullOrEmpty())
            {
                variantsId = entity.variantsId.Split(',').ToList();
            }
            else
            {
                variantsId.Add("");
            }

            if (CurrentUserId > 0)//在线客户
            {
                jm = await _cartServices.Add(CurrentUserId, entity.ProductId, variantsId, qty: 1, numType: 2, cartTypes: 2, CurrentLang, OvId: entity.ovId);
                if (jm.status)
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserId);
                    jm.otherData = count;

                    //更新jwt Token
                    await SetTokenCartCount(count);
                }

            }
            else//访客购买
            {
                jm = await _cartServices.Add(CurrentUserSessionId, entity.ProductId, variantsId, qty: 1, numType: 2, cartTypes: 2, CurrentLang, OvId: entity.ovId);
                if (jm.status)
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserSessionId);
                    jm.otherData = count;

                    //设置session
                    CurrentCartCount = count;
                    //设置session
                    //HttpContext.Session.SetString("cartCount", count.ToString());

                }
            }


            //立即购买，跳转到结算页面
            if (jm.status)
            {

                #region 添加赠品
                //获取用户币种
                var currency = await _currencyService.GetCurrency(CurrentCurrency);

                //获取后台币种
                var defaultCurrency = await _currencyService.GetManageDefaultCurrency();
                var cartData = new WebApiCallBack<CartDto>();
                //判断是否有赠品
                if (CurrentUserId > 0)//在线客户
                {
                    cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency);
                }
                else
                {
                    cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency);
                }

                var GiftCIdList = new List<int>();
                if (cartData.status == true)
                {
                    //添加赠品
                    if (CurrentUserId > 0)
                    {
                        var gs = await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserId, CurrentLang);
                        if (gs.status == true)
                        {
                            GiftCIdList = gs.data;
                        }
                    }
                    else
                    {
                        var gs = await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserSessionId, CurrentLang);
                        if (gs.status == true)
                        {
                            GiftCIdList = gs.data;
                        }
                    }
                }

                #endregion
                List<int> CIdList = new List<int>
                {
                    jm.data.ObjToInt()
                };

                if(GiftCIdList!=null && GiftCIdList.Any())
                {
                    CIdList.AddRange(GiftCIdList);
                }

                //string uuid = Guid.NewGuid().ToUUID();
                string idparam = HttpUtility.UrlEncode(string.Join(',', CIdList)).ToBase64();
                jm.data = new { location = $"/cart/checkout?data={idparam}&buynow=1" };


            }


            jm.msg = _localizer[jm.msg];

            return Json(jm);
        }


        #endregion



        #region 删除购物车
        /// <summary>
        /// 删除购物车
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        //[Authorize]
        [Route("/cart/remove/{cId}")]
        public async Task<IActionResult> DeleteCart(int cId)
        {
            var jm = new WebApiCallBack();
            //获取对象
            //int cId = Request.GetFormInt("cId");

            //重新获取购物车


            if (CurrentUserId > 0)//在线客户
            {
                jm = await _cartServices.DeleteCart(cId, CurrentUserId);
                if (jm.status)
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserId);
                    jm.otherData = count;


                    //更新jwt Token
                    await SetTokenCartCount(count);
                }
            }
            else
            {

                jm = await _cartServices.DeleteCart(cId, CurrentUserSessionId);
                if (jm.status)
                {
                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserSessionId);
                    jm.otherData = count;

                    //设置session
                    CurrentCartCount = count;
                }

            }

            #region  重新添加赠品
            //获取用户币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();
            var cartData = new WebApiCallBack<CartDto>();
            //判断是否有赠品
            if (CurrentUserId > 0)//在线客户
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency);
            }
            else
            {
                cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency);
            }

            if (cartData.status == true)
            {
                //添加赠品
                if (CurrentUserId > 0)
                {
                    await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserId, CurrentLang);
                }
                else
                {
                    await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserSessionId, CurrentLang);
                }


            }

            #endregion



            if (CurrentUserId > 0)//在线客户
            {
                //获取购物车数量
                int count = await _cartServices.GetCountAsync(CurrentUserId);
                jm.otherData = count;

                //更新jwt Token
                await SetTokenCartCount(count);

            }
            else
            {
                //获取购物车数量
                int count = await _cartServices.GetCountAsync(CurrentUserSessionId);
                jm.otherData = count;

                //设置session
                CurrentCartCount = count;
            }

            return Ok(new { ret = jm.status ? 1 : 0, msg = "" });

        }


        #endregion


        #region 删除全部购物车
        /// <summary>
        /// 删除购物车
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> DoDeleteAll()
        {
            var jm = new WebApiCallBack();

            var cid_list = Request.GetFormString("cid_list");

            List<int> CIdList = cid_list.Split(',').Select(it => int.Parse(it)).ToList();

            if (CurrentUserId > 0)//在线客户
            {
                jm = await _cartServices.DeleteByUserAsync(CurrentUserId, CIdList);
                if (jm.status)
                {
                    //删除赠品
                    var delGift = await _cartServices.DeleteAsync(it => it.UserId == CurrentUserId && it.SessionId == "" && it.BuyType == 5);

                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserId);
                    jm.otherData = count;


                    //更新jwt Token
                    await SetTokenCartCount(count);
                }
            }
            else
            {
                jm = await _cartServices.DeleteByUserAsync(CurrentUserSessionId, CIdList);
                if (jm.status)
                {
                    //删除赠品
                    var delGift = await _cartServices.DeleteAsync(it => it.UserId == 0 && it.SessionId == CurrentUserSessionId && it.BuyType == 5);

                    //获取购物车数量
                    int count = await _cartServices.GetCountAsync(CurrentUserSessionId);
                    jm.otherData = count;

                    //设置session
                    CurrentCartCount = count;
                }
            }
            return Ok(new { ret = jm.status ? 1 : 0, msg = "" });
        }


        #endregion


        #region 修改购物车
        /// <summary>
        /// 修改购物车
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> ModifyCart()
        {
            var jm = new WebApiCallBack();
            int cId = Request.GetFormInt("cId");
            int nums = Request.GetFormInt("nums");
            int ProId = Request.GetFormInt("ProId");
            string CIdAry = Request.GetFormString("CIdAry");

            CartResponse resp = new CartResponse();

            var cartData = new WebApiCallBack<CartDto>();
            var CId_List = !CIdAry.IsNullOrEmpty() ? CIdAry.Split(',').Distinct().Select(it => int.Parse(it)).ToList() : new List<int>();

            //获取币种
            var currency = await _currencyService.GetCurrency(CurrentCurrency);

            //获取后台币种
            var defaultCurrency = await _currencyService.GetManageDefaultCurrency();

            if (CurrentUserId > 0)
            {
                //更新数据
                jm = await _cartServices.SetCartNum(cId, ProId, nums, CurrentUserId, 2);

                //获取购物车详情
                cartData = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency, CId_List);

            }
            else
            {
                //更新数据
                jm = await _cartServices.SetCartNum(cId, ProId, nums, CurrentUserSessionId, 2);

                //获取购物车详情
                cartData = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency, CId_List);
            }


            #region  重新添加赠品
            if (cartData.status == true)
            {

                //添加赠品
                if (CurrentUserId > 0)
                {
                    await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserId, CurrentLang);
                }
                else
                {
                    await _cartServices.AddCartGifts(cartData.data.DiscountResult.Gifts, CurrentUserSessionId, CurrentLang);
                }

            }

            #endregion


            int totalCount = 0;
            if (CurrentUserId > 0)//在线客户
            {
                //获取购物车数量
                totalCount = await _cartServices.GetCountAsync(CurrentUserId);
                jm.otherData = totalCount;

                //更新jwt Token
                await SetTokenCartCount(totalCount);

            }
            else
            {
                //获取购物车数量
                totalCount = await _cartServices.GetCountAsync(CurrentUserSessionId);
                jm.otherData = totalCount;

                //设置session
                CurrentCartCount = totalCount;
            }



            //获取当前id产品
            var cartItem = cartData.data.ProductList.Where(it => it.CId == cId && it.productId == ProId).FirstOrDefault();
            if (cartItem == null)
            {
                jm.msg = _localizer["checkout.checkout.invalidCart"];
                jm.status = false;
                return Json(jm);
            }

            //赠品
            StringBuilder gifts_html = new StringBuilder();
            var cartResult = new WebApiCallBack<CartDto>();
            if (CurrentUserId > 0)//在线客户
            {
                cartResult = await _cartServices.GetCartInfos(CurrentUserId, currency, defaultCurrency);
            }
            else
            {
                cartResult = await _cartServices.GetCartInfos(CurrentUserSessionId, currency, defaultCurrency);
            }
            var giftItem = cartResult.data.list.Where(it => it.BuyType == 5).ToList();

            foreach (var product in giftItem)
            {
                StringBuilder attr = new StringBuilder();
                if (product.isMultiprop)
                {
                    foreach (var prop in product.multiprop)
                    {
                        attr.Append($"<p class=\"attr_{prop.Key}\"><b>{prop.Key}</b>: {prop.Value}</p>");
                    }
                }
                string htmlContent = $@"
                    <div cid=""{product.CId}"" data-proid=""{product.productId}"" class=""tr    gifts_item "">
                        <div class=""td prod_select"">
                            <input type=""checkbox"" name=""select"" value=""{product.CId}"" class="""" checked="""">
                        </div>
                        <div class=""td prod_info_detail"">
                            <div class=""prod_pic""><a href=""/products/{product.products.PageUrl}"" title=""{{product.products.Name_en}}"" class=""pic_box""><img src=""{product.PicPath}?x-oss-process=image/format,webp/resize,m_lfit,h_120,w_120"" alt=""{product.products.Name_en}"" class=""img_loading""></a></div>
                            <div class=""prod_info_box"">
                                <div class=""prod_info"">
                                    <div class=""invalid FontBgColor"">{_localizer["cart.global.invalid"]}</div>
                                    <h4 class=""prod_name""><a href=""/products/{product.products.PageUrl}"">{product.products.Name_en}</a></h4>
                                    <div class=""global_pro_info_text"">
                                        <div class=""psku""><b>SKU:</b> {product.sku}</div>
                                        <div class=""prod_attr"">
                                                {attr.ToString()}   
                                        </div>
                                    </div>
                                    <div class=""custom_attr""></div><p class=""remark"" style=""display:none;"">Remark: <span></span></p>
                                    <div class=""gifts_tips""><i class=""iconfont icon-gift1""></i>  Free Gift</div>
                                </div>
                                <div class=""prod_price notranslate""><p price=""{product.price}"" discount=""100"">{product.priceFormat.Item2}</p></div>
                                <div class=""prod_quantity"" start=""1"" wholesale=""wholesale"">
                                    <div class=""quantity_box clearfix"" data=""{"changeQty":1}"">
                                        <button type=""button"" class=""cut""><span class=""icon"">-</span></button>
                                        <div class=""qty""><input type=""text"" name=""Qty[]"" value=""{product.nums}"" maxlength=""6""></div>
                                        <button type=""button"" class=""add""><span class=""icon"">+</span></button>
                                    </div>
                                    <input type=""hidden"" name=""S_Qty[]"" value=""{product.nums}"">
                                    <input type=""hidden"" name=""CId[]"" value=""{product.CId}"">
                                    <input type=""hidden"" name=""ProId[]"" value=""{product.productId}"">
                                    <div class=""gifts_qty"">1 </div> 
                                </div>
                                <div class=""prod_total_price notranslate"">
                                    <p price=""{product.itemTotal}"" data-price=""{product.itemTotal}"">{product.itemTotalFormat.Item2}</p>
                                </div>
                            </div>
                        </div>
                    </div>";
                gifts_html.Append(htmlContent);
            }



            //int totalCount = cartData.data.ProductList.Sum(it => it.nums);

            resp.ret = cartData.status ? 1 : 0;
            if (cartData.status)
            {
                resp.msg = new Msg
                {
                    qty = nums,
                    price = new Dictionary<string, decimal>
                    {
                        {cId.ToString(),cartItem.priceFormat.Item1 }
                    },
                    amount = new Dictionary<string, decimal>
                    {
                         {cId.ToString(),cartItem.priceFormat.Item1*cartItem.nums }
                    },
                    total_count = totalCount,
                    total_price = cartData.data.goodsAmountFormat.Item1,//产品金额
                    total_weight = cartData.data.weightKg,
                    cutprice = cartData.data.orderPromotionMoneyFormat.Item1, //满额减价
                    IsCoupon = 0,
                    cart_qty = cartData.data.list.Count,
                    FullCondition = null,
                    gifts_html = gifts_html.ToString(),
                    disCountAry = cartData.data.DiscountResult,
                    CIdAry = CId_List.Select(it => it.ToString()).ToList(),
                    maqError = new List<object>(),
                    maxOQError = new List<object>(),
                    disableTips = null,
                    nowCartId = cartData.data.list.Select(it => it.CId).ToList(),
                    checkoutUrl = "",
                    webPrice = new WebPrice
                    {
                        total_price = cartData.data.amountFormat.Item1,
                        cutprice = 0,
                    }
                };
            }


            return Content(resp.ToJson());
        }

        #endregion



        #region 更新购物车

        /// <summary>
        /// 更新购物车
        /// </summary>
        /// <returns></returns>
        [HttpPost]
        //[Authorize]
        public async Task<IActionResult> UpdateCart()
        {
            var jm = new WebApiCallBack();

            if (CurrentUserId > 0)
            {
                jm = await _cartServices.UpdateCart(CurrentUserId);

            }
            else
            {
                jm = await _cartServices.UpdateCart(CurrentUserSessionId);
            }
            return Ok(new { ret = jm.status ? 1 : 0, msg = _localizer[jm.msg] });
        }


        #endregion


        #region 验证购物车产品
        /// <summary>
        /// 验证购物车产品
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        [HttpPost]

        public async Task<IActionResult> CheckoutCart()
        {
            string CIdAry = Request.GetFormString("id[]");
            if (CIdAry.IsNullOrEmpty())
            {
                return Ok(new { ret = 0, msg = new { location = "/cart" } });
            }
            List<int> id = CIdAry.Split(',').Select(it => int.Parse(it)).ToList();

            var jm = new WebApiCallBack();
            if (CurrentUserId > 0)
            {
                jm = await _cartServices.CheckoutCart(CurrentUserId, id);
            }
            else
            {
                jm = await _cartServices.CheckoutCart(CurrentUserSessionId, id);
            }

            if (jm.status == false)
            {
                return Ok(new
                {
                    ret = 0,
                    msg = _localizer[jm.msg]
                });
            }

            //string uuid = Guid.NewGuid().ToUUID();


            string idparam = HttpUtility.UrlEncode(CIdAry).ToBase64();

            return Ok(new
            {
                ret = jm.status ? 1 : 0,
                msg = new { location = $"/cart/checkout?data={idparam}" }
            });
        }

        #endregion

    }
}
