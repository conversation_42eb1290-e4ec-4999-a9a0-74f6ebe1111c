(function ($) {
    "use strict";
   
  
    $(window).on('load', function () {
        $(".preloader").fadeOut("slow");
    });
    $(window).scroll(function () {
        if ($(this).scrollTop() > 100) {
            $('.navbar').addClass("fixed-top");
        } else {
            $('.navbar').removeClass("fixed-top");
        }
    });
    $('.category-btn').on('click', function () {
        $(".main-category").toggle();
    });
    var selectors = {
        body: 'body',
        sitenav: '#siteNav',
        navLinks: '#siteNav .nav-item > a',
        menuToggle: '.js-mobile-nav-toggle',
        mobilenav: '.mobile-nav-wrapper',
        menuLinks: '#MobileNav .anm',
        closemenu: '.closemobileMenu'
    };

    $(selectors.navLinks).each(function () {
        if ($(this).attr('href') == window.location.pathname) $(this).addClass('active');
        else $(this).removeClass('active');
    })

    //$(document).on('click','#siteNav .nav-item>a', function () {
    //    $(this).addClass('active');
    //    $(this).parent().siblings().children().removeClass('active');
    //});
    //$(document).on('click', function () {
    //    console.log(window.location.pathname);
    //    $(selectors.navLinks).each(function (item, index) {
    //        if ($(index).attr('href') == window.location.pathname) { $(index).addClass('active');  }
    //        else $(index).removeClass('active');
    //    });
    //});
    $(".sidebar li a").each(function () {
        if ($(this).attr('href') == window.location.pathname) $(this).addClass('active');
        else if (window.location.pathname == "/Account/AddPayment") $(".sidebar li .payment-link").addClass('active');
        else if (window.location.pathname == "/Account/Addaddress") $(".sidebar li .address-link").addClass('active');
        else if (window.location.pathname == "/Account/ReviewDetail") $(".sidebar li .review-link").addClass('active');
        else if (window.location.pathname == "/Account/OrderDetail") $(".sidebar li .order-link").addClass('active');
        else if (window.location.pathname == "/Account/PointsRedeem") $(".sidebar li .points-link").addClass('active');
        else $(this).removeClass('active');
    })
    //$(".sidebar").on('click', function () {
    //    $(".sidebar li a").toggleClass('active');
    //})
    if ($('.search-box-outer').length) {
        $('.search-box-outer').on('click', function () {
            $('body').addClass('search-active');
        });
        $('.close-search').on('click', function () {
            $('body').removeClass('search-active');
        });
    }
    $(document).on('click', '.dropdown-menu a.dropdown-toggle', function (e) {
        if (!$(this).next().hasClass('show')) {
            $(this).parents('.dropdown-menu').first().find('.show').removeClass('show');
        }
        var $subMenu = $(this).next('.dropdown-menu');
        $subMenu.toggleClass('show');
        $(this).parents('li.nav-item.dropdown.show').on('hidden.bs.dropdown', function (e) {
            $('.dropdown-submenu .show').removeClass('show');
        });
        return false;
    });
   
    $(window).resize(function () {
        let ndr = $('.navbar-nav .nav-item.dropdown').slice(-2);
        if ($(window).width() > 991 && $(window).width() < 1199) {
            ndr.addClass("dropdown-right");
        } else {
            ndr.removeClass("dropdown-right");
        }
    });
    $(document).on('ready', function () {
        $("[data-background]").each(function () {
            $(this).css("background-image", "url(" + $(this).attr("data-background") + ")");
        });
    });
    new WOW().init();
    $('.hero-slider').owlCarousel({
        loop: true,
        nav: true,
        dots: true,
        margin: 0,
        autoplay: true,
        autoplayHoverPause: true,
        autoplayTimeout: 10000,
        items: 1,
        navText: ["<i class='far fa-angle-left'></i>", "<i class='far fa-angle-right'></i>"],
        onInitialized: function (event) {
            var $firstAnimatingElements = $('.hero-slider .owl-item').eq(event.item.index).find("[data-animation]");
            doAnimations($firstAnimatingElements);
        },
        onChanged: function (event) {
            var $firstAnimatingElements = $('.hero-slider .owl-item').eq(event.item.index).find("[data-animation]");
            doAnimations($firstAnimatingElements);
        }
    });

    function doAnimations(elements) {
        var animationEndEvents = 'webkitAnimationEnd mozAnimationEnd MSAnimationEnd oanimationend animationend';
        elements.each(function () {
            var $this = $(this);
            var $animationDelay = $this.data('delay');
            var $animationDuration = $this.data('duration');
            var $animationType = 'animated ' + $this.data('animation');
            $this.css({
                'animation-delay': $animationDelay,
                '-webkit-animation-delay': $animationDelay,
                'animation-duration': $animationDuration,
                '-webkit-animation-duration': $animationDuration,
            });
            $this.addClass($animationType).one(animationEndEvents, function () {
                $this.removeClass($animationType);
            });
        });
    }
    $('.product-slider').owlCarousel({
        items: 5,
        loop: true,
        margin: 15,
        smartSpeed: 400,
        nav: true,
        autoplay: false,
        autoplayHoverPause: true,
        dots: false,
        navText: ["<i class='far fa-angle-left'></i>", "<i class='far fa-angle-right'></i>"],
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 2
            },
            1000: {
                items: 3
            },
            1200: {
                items: 5
            }
        }
    });
    $('.product-slider2').owlCarousel({
        loop: true,
        nav: true,
        margin: 15,
        dots: false,
        smartSpeed: 400,
        autoplay: false,
        autoplayHoverPause: true,
        navText: ["<i class='far fa-angle-left'></i>", "<i class='far fa-angle-right'></i>"],
        items: 3,
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 2
            },
            1000: {
                items: 3
            },
            1200: {
                items: 3
            }
        },
    });
    $('.deal-slider').owlCarousel({
        items: 1,
        loop: true,
        margin: 15,
        smartSpeed: 400,
        nav: false,
        dots: true,
        autoplayHoverPause: true,
        autoplay: false,
        navText: ["<i class='far fa-angle-left'></i>", "<i class='far fa-angle-right'></i>"],
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 1
            },
            1000: {
                items: 1
            }
        }
    });
    $('.testimonial-slider').owlCarousel({
        loop: true,
        margin: 20,
        nav: false,
        dots: true,
        autoplay: true,
        responsive: {
            0: {
                items: 1
            },
            600: {
                items: 2
            },
            1000: {
                items: 3
            },
            1400: {
                items: 4
            }
        }
    });
    $('.brand-slider').owlCarousel({
        loop: true,
        margin: 20,
        nav: false,
        dots: false,
        autoplay: false,
        autoplayHoverPause: true,
        responsive: {
            0: {
                items: 2
            },
            600: {
                items: 3
            },
            1000: {
                items: 6
            }
        }
    });
    $('.category-slider').owlCarousel({
        loop: true,
        margin: 20,
        nav: false,
        dots: false,
        autoplay: false,
        responsive: {
            0: {
                items: 2
            },
            600: {
                items: 3
            },
            1000: {
                items: 4
            },
            1200: {
                items: 6
            },
            1400: {
                items: 6
            }
        }
    });
    $('.instagram-slider').owlCarousel({
        loop: true,
        margin: 20,
        nav: false,
        dots: false,
        autoplay: true,
        responsive: {
            0: {
                items: 2
            },
            600: {
                items: 3
            },
            1000: {
                items: 5
            }
        }
    });
    $('.counter').countTo();
    $('.counter-box').appear(function () {
        $('.counter').countTo();
    }, {
        accY: -100
    });
    $(".popup-gallery").magnificPopup({
        delegate: '.popup-img',
        type: 'image',
        gallery: {
            enabled: true
        },
    });
    $(".popup-youtube, .popup-vimeo, .popup-gmaps").magnificPopup({
        type: "iframe",
        mainClass: "mfp-fade",
        removalDelay: 160,
        preloader: false,
        fixedContentPos: false
    });
    $(window).scroll(function () {
        if (document.body.scrollTop > 100 || document.documentElement.scrollTop > 100) {
            $("#scroll-top").addClass('active');
        } else {
            $("#scroll-top").removeClass('active');
        }
    });
    $("#scroll-top").on('click', function () {
        $("html, body").animate({
            scrollTop: 0
        }, 1500);
        return false;
    });
    $('[data-countdown]').each(function () {
        let finalDate = $(this).data('countdown');
        $(this).countdown(finalDate, function (event) {
            $(this).html(event.strftime('<div class="row"><div class="col countdown-item"><h2>%-D</h2><h5>Day%!d</h5></div><div class="col countdown-item"><h2>%H</h2><h5>Hours</h5></div><div class="col countdown-item"><h2>%M</h2><h5>Minutes</h5></div><div class="col countdown-item"><h2>%S</h2><h5>Seconds</h5></div></div>'));
        });
    });
    let date = new Date().getFullYear();
    $("#date").html(date);
    $('.select').niceSelect();

   
    if ($(".price-range").length) {
        $(".price-range").slider({
            range: true,
            min: 0,
            max: 5000,
            values: [500, 2000],
            slide: function (event, ui) {
                $("#price-amount").val("$" + ui.values[0] + " - $" + ui.values[1]);
            }
        });
        $("#price-amount").val("$" + $(".price-range").slider("values", 0) + " - $" + $(".price-range").slider("values", 1));
    }
    $(".plus-btn").on("click", function () {
        var i = $(this).closest(".shop-cart-qty").children(".quantity").get(0).value++,
            c = $(this).closest(".shop-cart-qty").children(".minus-btn");
        i > 0 && c.removeAttr("disabled");
    }), $(".minus-btn").on("click", function () {
        2 == $(this).closest(".shop-cart-qty").children(".quantity").get(0).value-- && $(this).attr("disabled", "disabled");
    })
    if ($('.flexslider-thumbnails').length) {
        $('.flexslider-thumbnails').flexslider({
            animation: "slide",
            controlNav: "thumbnails",
        });
    }
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-tooltip="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    })
    $(".profile-img-btn").on('click', function () {
        $(".profile-img-file").click();
    });
    if ($('.message-content-info').length) {
        $(function () {
            var chatbox = $('.message-content-info');
            var chatheight = chatbox[0].scrollHeight;
            chatbox.scrollTop(chatheight);
        });
    }
    $(".Learn-more-btn").on('click',function () {
        $(this).text(function (i, text) {
            return text === "Close" ? "Learn more" : "Close";
        })
        if ($(this).prev().hasClass('active')) {
            $(this).prev().removeClass('active');
        } else {
            $(this).prev().addClass('active');
        }
    })
    //$(".sidebar-list").on('click', function () {
    //    $(".sidebar-list li a").toggleClass('active');
        
    //})
    $(window).on('load', function () {
        setTimeout(function () {
            $("#popup-banner").modal("show");
        }, 3000)
    });
    $('.invoice-print-btn').click(function () {
        $('.invoice-print-btn').hide();
        $('.invoice-container').removeClass('not-print');
        window.print();
        $('.invoice-container').addClass('not-print');
        $('.invoice-print-btn').show();
    });
    /*----------------------------
     23. Product Sticky Bottom Cart
     ------------------------------ */
    function shopLook_sticky_cart() {
        window.onscroll = function () {
            $(window).scrollTop() > 600 && $(".stickyCart").length ? (
                $("body").css("padding-bottom", "70px"),
                $(".stickyCart").slideDown()) : ($("body").css("padding-bottom", "0"),
                    $(".stickyCart").slideUp());
        };
        $(".stickyOptions .selectedOpt").on("click", function () {
            $(".stickyOptions ul").slideToggle("fast");
        }),
            $(".stickyOptions .vrOpt").on("click", function (e) {
                var t = $(this).attr("data-val"),
                    s = $(this).attr("data-no"),
                    a = $(this).text();
                $(".selectedOpt").text(a), $(".stickyCart .selectbox").val(t).trigger("change"), $(".stickyOptions ul").slideUp("fast"), this.productvariants = JSON.parse(document.getElementById("ProductJson-" + i).innerHTML), $(".stickyCart .product-featured-img").attr("src", this.productvariants.variants[s].featured_image.src.replace(/(\.[^\.]*$|$)/, "_60x60$&"));
            });
    }
    shopLook_sticky_cart();

})(jQuery);
